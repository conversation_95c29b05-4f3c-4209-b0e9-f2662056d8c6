// 阿里云DashScope ASR提供商
// 🔧 重写：基于官方WebSocket API实现 - 长连接实时流模式
import * as WebSocket from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { ASRServiceInterface, ASRResult, ASROptions, ASRConfig, ASRProvider, ASRError, DashScopeConfig } from '../../../types/asr.js';
import { systemMonitor } from '../../../monitoring/SystemMonitor.js';

// 🔥 任务状态枚举（严格按照官方文档）
enum TaskState {
  IDLE = 'idle',           // 空闲状态，可以开始新任务
  STARTING = 'starting',   // 已发送run-task，等待task-started
  ACTIVE = 'active',       // 任务活跃，可以发送音频
  FINISHED = 'finished'    // 任务已结束，连接可复用
}

// 实时流回调接口
export interface StreamingCallbacks {
  onPartialResult: (text: string, bubbleId: string) => void;
  onFinalResult: (text: string, bubbleId: string) => void;
  onError: (error: ASRError) => void;
}

export class DashScopeProvider extends EventEmitter implements ASRServiceInterface {
  private config: ASRConfig;
  private dashscopeConfig: DashScopeConfig;
  private ws: WebSocket | null = null;
  private currentTaskId: string | null = null;
  private currentBubbleId: string | null = null;
  private isConnected: boolean = false;
  private taskStarted: boolean = false;
  private isStreaming: boolean = false;
  private streamingCallbacks: StreamingCallbacks | null = null;
  private lastAudioTime: number = 0;
  private totalAudioSent: number = 0;
  private silenceTimer: NodeJS.Timeout | null = null;
  private SILENCE_TIMEOUT = 3000; // 3秒静音超时，触发句子结束

  // 🔥 任务状态管理（连接复用支持）
  private taskState: TaskState = TaskState.IDLE;
  private pendingAudioBuffers: Buffer[] = []; // 等待task-started时的音频缓冲

  // 🔥 心跳机制相关属性（基于官方文档优化）
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastPongTime: number = 0;
  private heartbeatIntervalMs: number = 30000; // 30秒心跳间隔（官方推荐，远小于60秒超时）
  private pongTimeoutMs: number = 20000; // 20秒pong超时（给足够响应时间）
  private maxIdleTime: number = 50000; // 50秒最大空闲时间（在官方60秒超时前主动处理）
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 3; // 减少重连次数，避免过度重试
  private reconnectBaseDelay: number = 1000; // 1秒基础重连延迟
  private reconnectMaxDelay: number = 30000; // 30秒最大重连延迟
  private lastConnectionAttempt: number = 0; // 🔥 记录连接尝试时间，用于监控
  private reconnectTimer: NodeJS.Timeout | null = null; // 🔥 保存重连定时器引用

  constructor() {
    super();
    this.config = {
      provider: ASRProvider.DASHSCOPE,
      priority: 1,  // 🔥 最高优先级
      timeout: 30000,  // 长连接需要更长的超时时间
      retryCount: 3,
      weight: 1.5,  // 最高权重
      enabled: true
    };

    this.dashscopeConfig = {
      apiKey: process.env.DASHSCOPE_API_KEY || '',
      model: 'paraformer-realtime-v2',  // 官方推荐的实时识别模型
      endpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/inference/',  // 🔧 WebSocket端点
      timeout: 30000
    };

    console.log('🚀 DashScope ASR Provider initialized with streaming model:', this.dashscopeConfig.model);
  }

  getName(): string {
    return ASRProvider.DASHSCOPE;
  }

  isAvailable(): boolean {
    const available = this.config.enabled && !!this.dashscopeConfig.apiKey;
    if (!available) {
      console.warn('⚠️ DashScope ASR not available - API key missing or service disabled');
    }
    return available;
  }

  getConfig(): ASRConfig {
    return { ...this.config };
  }

  updateConfig(config: Partial<ASRConfig>): void {
    this.config = { ...this.config, ...config };
  }

  private createError(message: string, code: string, retryable: boolean): ASRError {
    const error = new Error(message) as ASRError;
    error.name = 'ASRError';
    error.code = code;
    error.retryable = retryable;
    error.provider = ASRProvider.DASHSCOPE;
    error.timestamp = Date.now();
    return error;
  }

  async destroy(): Promise<void> {
    console.log('🔄 DEBUG: DashScope destroy() called');

    // 🔧 清理WebSocket连接和流式会话
    this.stopHeartbeat();
    console.log('🔄 DEBUG: Heartbeat stopped');

    // 🔥 停止重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
      console.log('🔄 DEBUG: Reconnect timer cleared');
    } else {
      console.log('🔄 DEBUG: No reconnect timer to clear');
    }

    // 🔥 重置重连状态，防止继续重连
    this.reconnectAttempts = this.maxReconnectAttempts;
    console.log(`🔄 DEBUG: Reconnect attempts set to max (${this.maxReconnectAttempts}) to prevent further reconnection`);

    // 🔥 清理流式回调，防止僵尸回调
    this.isStreaming = false;
    this.streamingCallbacks = null;
    console.log('🔄 DEBUG: Streaming callbacks cleared');

    // 🔥 清理任务状态
    this.currentTaskId = null;
    this.currentBubbleId = null;
    this.taskStarted = false;
    console.log('🔄 DEBUG: Task state cleared');

    await this.stopStreamingSession();
    console.log('🔄 DEBUG: DashScope ASR Provider destroyed completely');
  }

  /**
   * 🔥 启动心跳机制
   */
  private startHeartbeat(): void {
    this.stopHeartbeat(); // 确保没有重复的心跳定时器

    this.lastPongTime = Date.now();
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatIntervalMs);

    console.log('💓 DashScope: Heartbeat mechanism started', {
      interval: this.heartbeatIntervalMs,
      timeout: this.pongTimeoutMs
    });
  }

  /**
   * 🔥 停止心跳机制
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      console.log('💓 DashScope: Heartbeat mechanism stopped');
    }
  }

  /**
   * 🔥 发送心跳ping消息
   */
  private sendHeartbeat(): void {
    if (!this.ws || this.ws.readyState !== 1) { // WebSocket.OPEN = 1
      console.warn('💓 DashScope: Cannot send heartbeat - WebSocket not ready');
      this.handleConnectionLost();
      return;
    }

    try {
      // 发送ping消息
      this.ws.ping();
      console.log('💓 DashScope: Heartbeat ping sent');

      // 检查上次pong的时间
      this.checkPongTimeout();
    } catch (error) {
      console.error('💓 DashScope: Failed to send heartbeat ping:', error);
      this.handleConnectionLost();
    }
  }

  /**
   * 🔥 检查pong超时
   */
  private checkPongTimeout(): void {
    const now = Date.now();
    const timeSinceLastPong = now - this.lastPongTime;

    if (timeSinceLastPong > this.pongTimeoutMs) {
      console.warn('💓 DashScope: Pong timeout detected', {
        timeSinceLastPong,
        timeout: this.pongTimeoutMs
      });
      this.handleConnectionLost();
    }
  }

  /**
   * 🔥 处理连接丢失
   */
  private handleConnectionLost(): void {
    console.error('🔌 DashScope: Connection lost detected');

    this.stopHeartbeat();
    this.isConnected = false;
    this.taskStarted = false;
    this.taskState = TaskState.IDLE;

    // 触发重连
    this.attemptReconnect();
  }

  /**
   * 🔥 尝试重连（指数退避策略）
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🔌 DashScope: Max reconnection attempts reached', {
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts
      });

      // 通知错误
      if (this.streamingCallbacks) {
        this.streamingCallbacks.onError(
          this.createError('Max reconnection attempts reached', 'MAX_RECONNECT_ATTEMPTS', false)
        );
      }
      return;
    }

    this.reconnectAttempts++;

    // 🔥 指数退避算法：delay = baseDelay * 2^(attempts-1) + randomJitter
    const exponentialDelay = this.reconnectBaseDelay * Math.pow(2, this.reconnectAttempts - 1);
    const cappedDelay = Math.min(exponentialDelay, this.reconnectMaxDelay);
    const jitter = Math.random() * 1000; // 0-1秒随机抖动，防止惊群效应
    const finalDelay = cappedDelay + jitter;

    console.log('🔄 DashScope: Scheduling reconnection attempt', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      delay: Math.round(finalDelay),
      exponentialDelay: Math.round(exponentialDelay),
      jitter: Math.round(jitter)
    });

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null; // 清除引用
      try {
        console.log('🔄 DashScope: Attempting reconnection...');
        await this.createWebSocketConnection();

        // 重连成功，重置计数器
        this.reconnectAttempts = 0;
        console.log('✅ DashScope: Reconnection successful');

        // 如果正在流式处理，需要重新启动任务
        if (this.isStreaming && this.streamingCallbacks) {
          console.log('🔄 DashScope: Restarting streaming session after reconnection');
          // 这里可以根据需要重新启动流式会话
        }

      } catch (error) {
        console.error('❌ DashScope: Reconnection failed:', error);
        // 继续尝试重连
        this.attemptReconnect();
      }
    }, finalDelay);
  }

  /**
   * 🔥 重置重连状态
   */
  private resetReconnectState(): void {
    this.reconnectAttempts = 0;

    // 🔥 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
      console.log('🔄 DashScope: Reconnect timer cleared in reset');
    }

    console.log('🔄 DashScope: Reconnection state reset');
  }

  /**
   * 🔥 获取连接状态监控信息
   */
  getConnectionStatus(): {
    isConnected: boolean;
    taskState: string;
    isStreaming: boolean;
    reconnectAttempts: number;
    lastPongTime: number;
    timeSinceLastPong: number;
    heartbeatActive: boolean;
  } {
    const now = Date.now();
    return {
      isConnected: this.isConnected,
      taskState: this.taskState,
      isStreaming: this.isStreaming,
      reconnectAttempts: this.reconnectAttempts,
      lastPongTime: this.lastPongTime,
      timeSinceLastPong: now - this.lastPongTime,
      heartbeatActive: this.heartbeatInterval !== null
    };
  }

  /**
   * 🔥 输出详细的连接监控日志
   */
  logConnectionStatus(): void {
    const status = this.getConnectionStatus();
    const now = Date.now();
    console.log('📊 DashScope Connection Status:', {
      ...status,
      wsReadyState: this.ws?.readyState,
      wsReadyStateText: this.ws ? this.getWebSocketStateText(this.ws.readyState) : 'null',
      totalAudioSent: this.totalAudioSent,
      pendingBuffers: this.pendingAudioBuffers.length,
      // 🔥 新增心跳监控信息
      heartbeatStatus: {
        lastPongTime: this.lastPongTime,
        timeSinceLastPong: this.lastPongTime ? now - this.lastPongTime : 'never',
        heartbeatActive: !!this.heartbeatInterval,
        heartbeatInterval: this.heartbeatIntervalMs,
        pongTimeout: this.pongTimeoutMs,
        maxIdleTime: this.maxIdleTime
      },
      // 🔥 新增重连监控信息
      reconnectStatus: {
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
        baseDelay: this.reconnectBaseDelay,
        maxDelay: this.reconnectMaxDelay
      }
    });
  }

  /**
   * 🔥 获取WebSocket状态文本
   */
  private getWebSocketStateText(readyState: number): string {
    switch (readyState) {
      case 0: return 'CONNECTING';
      case 1: return 'OPEN';
      case 2: return 'CLOSING';
      case 3: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  /**
   * 🔥 启动实时流式识别会话
   */
  async startStreamingSession(callbacks: StreamingCallbacks): Promise<void> {
    if (this.isStreaming) {
      console.log('🔄 DashScope streaming session already active');
      return;
    }

    if (!this.isAvailable()) {
      throw this.createError('DashScope ASR service not available', 'SERVICE_UNAVAILABLE', false);
    }

    console.log('🚀 Starting DashScope streaming session...');

    this.streamingCallbacks = callbacks;
    this.isStreaming = true;

    // 🔥 初始化状态（连接复用支持）
    this.taskState = TaskState.IDLE;
    this.pendingAudioBuffers = [];
    this.totalAudioSent = 0;
    this.lastAudioTime = Date.now();

    try {
      await this.createWebSocketConnection();
      // 🔥 不在这里发送run-task，等待第一个音频数据时再发送

      console.log('✅ DashScope streaming session started successfully (connection ready for tasks)');
    } catch (error) {
      this.isStreaming = false;
      this.streamingCallbacks = null;
      this.taskState = TaskState.IDLE;
      throw error;
    }
  }

  /**
   * 🔥 发送音频数据到流（连接复用版 - 严格遵循官方文档）
   */
  addAudioData(audioBuffer: Buffer): void {
    if (!this.isStreaming) {
      console.log('⚠️ DashScope streaming session not active');
      return;
    }

    if (!this.ws) {
      console.log('⚠️ DashScope WebSocket is null');
      return;
    }

    // 🔥 连接复用逻辑：检查任务状态并自动启动新任务
    if (this.taskState === TaskState.FINISHED || this.taskState === TaskState.IDLE) {
      console.log(`🔄 Task state is ${this.taskState}, starting new task for audio processing`);

      // 缓存音频数据，等待task-started
      this.pendingAudioBuffers.push(audioBuffer);
      console.log(`📦 Buffered audio data: ${audioBuffer.length} bytes (pending task-started)`);

      // 异步启动新任务，不阻塞当前调用
      this.startNewTask().catch(error => {
        console.error('❌ Failed to start new task in addAudioData:', error);
      });

      return;
    }

    // 🔥 如果任务正在启动，缓存音频数据
    if (this.taskState === TaskState.STARTING) {
      this.pendingAudioBuffers.push(audioBuffer);
      console.log(`📦 Buffered audio data: ${audioBuffer.length} bytes (waiting for task-started)`);
      return;
    }

    // 🔥 任务活跃状态，直接发送音频
    if (this.taskState === TaskState.ACTIVE) {
      this.sendAudioToWebSocket(audioBuffer);
    } else {
      console.log(`⚠️ Unexpected task state: ${this.taskState}, buffering audio`);
      this.pendingAudioBuffers.push(audioBuffer);
    }
  }

  /**
   * 🔥 发送音频到WebSocket（内部方法）
   */
  private sendAudioToWebSocket(audioBuffer: Buffer): void {
    try {
      this.ws!.send(audioBuffer);
      this.totalAudioSent += audioBuffer.length;
      this.lastAudioTime = Date.now();

      // 🔥 重置静音检测定时器
      this.resetSilenceTimer();

      console.log(`📤 ✅ Audio sent to DashScope: ${audioBuffer.length} bytes (total: ${this.totalAudioSent} bytes)`);
    } catch (error) {
      console.error('❌ Failed to send audio to DashScope:', error);
      console.error(`🔍 WebSocket state during error: readyState=${this.ws?.readyState}, taskState=${this.taskState}`);

      if (this.streamingCallbacks) {
        this.streamingCallbacks.onError(
          this.createError('Failed to send audio data', 'AUDIO_SEND_ERROR', true)
        );
      }
    }
  }

  /**
   * 🔥 启动新任务（连接复用，修复异步处理）
   */
  private async startNewTask(): Promise<void> {
    // 生成新的task_id和bubble_id
    this.currentTaskId = uuidv4();
    this.currentBubbleId = uuidv4();
    this.taskState = TaskState.STARTING;
    this.taskStarted = false;
    this.totalAudioSent = 0;
    this.pendingAudioBuffers = [];

    console.log(`🆕 Starting new task: ${this.currentTaskId}, bubble: ${this.currentBubbleId}`);

    try {
      // 发送run-task指令，正确处理异步调用
      await this.sendRunTask();
    } catch (error) {
      console.error(`❌ Failed to start new task: ${error}`);

      // 重置状态
      this.taskState = TaskState.IDLE;
      this.taskStarted = false;
      this.pendingAudioBuffers = [];

      // 🔥 修复：只有在真正的错误情况下才通知回调
      const errorMessage = (error as Error).message;
      if (!errorMessage.includes('waiting for task-started') || !this.isConnected) {
        // 只有在连接真正断开或其他严重错误时才通知回调
        if (this.streamingCallbacks) {
          this.streamingCallbacks.onError(
            this.createError(`Failed to start new task: ${errorMessage}`, 'TASK_START_ERROR', true)
          );
        }
      } else {
        // 对于task-started等待超时，只记录警告
        console.warn('⚠️ Task start timeout, but connection is active. Audio processing may still work.');
      }
    }
  }

  /**
   * 🔥 处理缓存的音频数据（task-started后）
   */
  private processPendingAudio(): void {
    if (this.pendingAudioBuffers.length > 0) {
      console.log(`📦 Processing ${this.pendingAudioBuffers.length} pending audio buffers`);

      for (const buffer of this.pendingAudioBuffers) {
        this.sendAudioToWebSocket(buffer);
      }

      this.pendingAudioBuffers = [];
      console.log(`✅ All pending audio buffers processed`);
    }
  }

  /**
   * 🔥 停止实时流式识别会话
   */
  async stopStreamingSession(): Promise<void> {
    if (!this.isStreaming) {
      return;
    }

    console.log('🛑 Stopping DashScope streaming session...');

    this.isStreaming = false;
    this.streamingCallbacks = null;

    // 🔥 清理静音检测定时器
    this.clearSilenceTimer();

    // 🔥 停止心跳机制
    this.stopHeartbeat();

    // 结束任务
    if (this.taskStarted && this.taskState === TaskState.ACTIVE) {
      await this.sendFinishTask();
    }

    // 关闭连接
    if (this.ws) {
      this.ws.close(1000, 'Session ended normally'); // 正常关闭，不触发重连
      this.ws = null;
    }

    // 🔥 重置所有状态
    this.isConnected = false;
    this.taskStarted = false;
    this.taskState = TaskState.IDLE;
    this.currentTaskId = null;
    this.currentBubbleId = null;
    this.pendingAudioBuffers = [];
    this.totalAudioSent = 0;
    this.resetReconnectState(); // 🔥 重置重连状态

    console.log('✅ DashScope streaming session stopped');
  }

  /**
   * 🔥 兼容性方法：单次识别（内部使用流式实现）
   */
  async recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult> {
    const startTime = Date.now();

    if (!this.isAvailable()) {
      throw this.createError('DashScope ASR service not available', 'SERVICE_UNAVAILABLE', false);
    }

    console.log(`🎯 DashScope ASR: Starting single recognition, audio size: ${audioBuffer.length} bytes`);

    return new Promise((resolve, reject) => {
      this.performSingleRecognition(audioBuffer, options, startTime, resolve, reject);
    });
  }

  /**
   * 🔧 单次识别实现（兼容性方法）
   */
  private async performSingleRecognition(
    audioBuffer: Buffer,
    options: ASROptions | undefined,
    startTime: number,
    resolve: (result: ASRResult) => void,
    reject: (error: ASRError) => void
  ): Promise<void> {
    let finalResult: string = '';
    let hasError = false;

    const callbacks: StreamingCallbacks = {
      onPartialResult: (text: string, bubbleId: string) => {
        // 单次识别模式下忽略部分结果
        console.log(`📝 Partial result: ${text}`);
      },
      onFinalResult: (text: string, bubbleId: string) => {
        finalResult = text;
        const processingTime = Date.now() - startTime;

        resolve({
          text: finalResult,
          confidence: 0.9,
          provider: ASRProvider.DASHSCOPE,
          processingTime,
          timestamp: Date.now(),
          isPartial: false,
          language: 'zh-cn'
        });
      },
      onError: (error: ASRError) => {
        hasError = true;
        reject(error);
      }
    };

    try {
      await this.startStreamingSession(callbacks);
      this.addAudioData(audioBuffer);

      // 等待一段时间让音频处理完成
      setTimeout(async () => {
        await this.stopStreamingSession();
      }, 3000);

    } catch (error) {
      hasError = true;
      reject(this.createError(`Single recognition error: ${(error as Error).message}`, 'PROCESSING_ERROR', true));
    }
  }

  /**
   * 🔧 创建新气泡
   */
  private createNewBubble(): void {
    this.currentBubbleId = uuidv4();
    this.lastAudioTime = Date.now();
    console.log(`🆕 Created new bubble: ${this.currentBubbleId}`);
  }

  /**
   * 🔧 重置静音检测定时器
   */
  private resetSilenceTimer(): void {
    // 清除现有定时器
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }

    // 设置新的静音检测定时器
    this.silenceTimer = setTimeout(() => {
      this.handleSilenceTimeout();
    }, this.SILENCE_TIMEOUT);
  }

  /**
   * 🔧 处理静音超时
   */
  private async handleSilenceTimeout(): Promise<void> {
    if (!this.isStreaming || !this.taskStarted) {
      return;
    }

    console.log(`🔇 Silence timeout detected (${this.SILENCE_TIMEOUT}ms), sending finish-task to trigger sentence end`);

    try {
      // 发送finish-task指令来触发句子结束
      await this.sendFinishTask();
      console.log(`✅ Finish-task sent due to silence timeout`);
    } catch (error) {
      console.error('❌ Failed to send finish-task on silence timeout:', error);
    }
  }

  /**
   * 🔧 清理静音检测定时器
   */
  private clearSilenceTimer(): void {
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }
  }

  /**
   * 🔧 创建WebSocket连接
   */
  private async createWebSocketConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 🔥 记录连接尝试时间
        this.lastConnectionAttempt = Date.now();

        // 🔧 验证API密钥
        if (!this.dashscopeConfig.apiKey) {
          throw new Error('DashScope API key is missing');
        }

        // 如果已有连接且状态正常，直接返回
        if (this.ws && this.isConnected && this.ws.readyState === WebSocket.OPEN) {
          console.log('🔄 Reusing existing DashScope WebSocket connection');
          resolve();
          return;
        }

        console.log(`🔗 Creating new DashScope WebSocket connection: ${this.dashscopeConfig.endpoint}`);
        console.log(`🔑 Using API key: ${this.dashscopeConfig.apiKey.substring(0, 10)}...`);

        this.ws = new WebSocket.WebSocket(this.dashscopeConfig.endpoint, {
          headers: {
            'Authorization': `bearer ${this.dashscopeConfig.apiKey}`, // 🔥 修复：使用小写bearer
            'X-DashScope-DataInspection': 'enable'
          }
        }) as any; // 🔥 临时类型断言，避免类型冲突

        this.ws!.on('open', () => {
          console.log('✅ DashScope WebSocket connected successfully');
          this.isConnected = true;
          this.resetReconnectState(); // 🔥 重置重连状态
          this.startHeartbeat(); // 🔥 启动心跳机制

          // 🔥 记录连接成功
          systemMonitor.recordDashScopeRequest(true, Date.now() - (this.lastConnectionAttempt || Date.now()));

          resolve();
        });

        this.ws!.on('error', (error: any) => {
          console.error('❌ DashScope WebSocket connection error:', {
            message: error?.message || 'Unknown error',
            code: error?.code || 'UNKNOWN',
            type: error?.type || 'UNKNOWN',
            endpoint: this.dashscopeConfig.endpoint
          });
          this.isConnected = false;
          this.stopHeartbeat(); // 🔥 停止心跳

          // 🔥 记录连接失败
          systemMonitor.recordDashScopeRequest(false, Date.now() - this.lastConnectionAttempt);

          reject(error);
        });

        // 🔥 添加pong事件处理
        this.ws!.on('pong', () => {
          this.lastPongTime = Date.now();
          console.log('💓 DashScope: Pong received, connection healthy');
        });

        this.ws!.on('close', (code, reason) => {
          console.log(`🔌 DEBUG: DashScope WebSocket closed: code=${code}, reason=${reason}`);
          this.isConnected = false;
          this.taskStarted = false;
          this.stopHeartbeat(); // 🔥 停止心跳

          // 🔥 根据关闭代码决定是否重连
          const shouldReconnect = code !== 1000 && code !== 1001; // 正常关闭不重连

          if (shouldReconnect && this.isStreaming) {
            console.log('🔄 DEBUG: Unexpected close, attempting reconnection...');
            this.handleConnectionLost();
          } else {
            // 🔥 连接关闭时立即清理回调，防止僵尸回调
            console.log('🔄 DEBUG: Clearing streaming callbacks due to connection close');
            this.isStreaming = false;
            this.streamingCallbacks = null;
            this.currentTaskId = null;
            this.currentBubbleId = null;
          }
        });

        // 设置消息处理器
        this.setupStreamingMessageHandlers();

        // 设置连接超时
        setTimeout(() => {
          if (!this.isConnected) {
            const timeoutError = new Error('DashScope WebSocket connection timeout (5s)');
            console.error('⏰ DashScope WebSocket connection timeout');
            reject(timeoutError);
          }
        }, 5000);

      } catch (error) {
        console.error('❌ Failed to create DashScope WebSocket:', error);
        reject(error);
      }
    });
  }

  /**
   * 🔧 设置流式消息处理器
   */
  private setupStreamingMessageHandlers(): void {
    if (!this.ws) return;

    this.ws!.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log('📨 DashScope streaming message received:', JSON.stringify(message, null, 2));

        // 🔥 添加详细的消息类型日志
        const event = message.header?.event;
        console.log(`🔍 Message event type: ${event}`);

        if (event === 'result-generated') {
          const sentence = message.payload?.output?.sentence;
          console.log(`🎯 Result-generated details:`, {
            text: sentence?.text,
            sentence_end: sentence?.sentence_end,
            end_time: sentence?.end_time,
            begin_time: sentence?.begin_time
          });
        }

        this.handleStreamingMessage(message);
      } catch (error) {
        console.error('❌ Failed to parse DashScope WebSocket message:', {
          error: (error as Error).message,
          data: data.toString().substring(0, 200) + '...'
        });

        if (this.streamingCallbacks) {
          this.streamingCallbacks.onError(
            this.createError('Failed to parse WebSocket message', 'PARSING_ERROR', false)
          );
        }
      }
    });
  }

  /**
   * 🔧 处理流式消息
   */
  private handleStreamingMessage(message: any): void {
    const event = message.header?.event;

    switch (event) {
      case 'task-started':
        console.log(`✅ DashScope streaming task started: ${this.currentTaskId}`);
        this.taskStarted = true;
        this.taskState = TaskState.ACTIVE;

        // 🔥 处理缓存的音频数据
        this.processPendingAudio();
        break;

      case 'result-generated':
        const sentence = message.payload?.output?.sentence;

        // 🔥 详细的句子结束检测日志
        console.log(`🔍 Processing result-generated event:`, {
          hasText: !!sentence?.text,
          text: sentence?.text,
          sentence_end: sentence?.sentence_end,
          end_time: sentence?.end_time,
          begin_time: sentence?.begin_time,
          heartbeat: sentence?.heartbeat,
          hasCallbacks: !!this.streamingCallbacks,
          currentBubbleId: this.currentBubbleId
        });

        // 🔥 跳过心跳消息
        if (sentence?.heartbeat === true) {
          console.log(`💓 Skipping heartbeat message`);
          break;
        }

        if (sentence?.text && this.streamingCallbacks && this.currentBubbleId) {
          console.log(`📝 DashScope streaming result: "${sentence.text}" (sentence_end: ${sentence.sentence_end}, end_time: ${sentence.end_time})`);

          // 🔥 根据sentence_end字段决定是部分结果还是最终结果
          if (sentence.sentence_end === true) {
            // 最终结果：完成当前气泡，创建新气泡
            console.log(`🎯 ✅ FINAL RESULT detected for bubble ${this.currentBubbleId}: "${sentence.text}"`);
            console.log(`🎯 Triggering LLM with final text: "${sentence.text}"`);

            // 🔥 检查回调是否还存在，防止僵尸回调
            if (this.streamingCallbacks) {
              this.streamingCallbacks.onFinalResult(sentence.text, this.currentBubbleId);
            } else {
              console.warn(`⚠️ DEBUG: Streaming callbacks cleared, ignoring final result: "${sentence.text}"`);
            }
            this.createNewBubble();
          } else {
            // 部分结果：更新当前气泡
            console.log(`📝 Partial result for bubble ${this.currentBubbleId}: "${sentence.text}"`);

            // 🔥 检查回调是否还存在，防止僵尸回调
            if (this.streamingCallbacks) {
              this.streamingCallbacks.onPartialResult(sentence.text, this.currentBubbleId);
            } else {
              console.warn(`⚠️ DEBUG: Streaming callbacks cleared, ignoring partial result: "${sentence.text}"`);
            }
          }
        } else {
          console.log(`⚠️ Skipping result-generated event:`, {
            reason: !sentence?.text ? 'no text' :
                   !this.streamingCallbacks ? 'no callbacks' :
                   !this.currentBubbleId ? 'no bubble id' : 'unknown',
            sentence_details: {
              text: sentence?.text,
              sentence_end: sentence?.sentence_end,
              heartbeat: sentence?.heartbeat
            }
          });
        }
        break;

      case 'task-finished':
        console.log(`✅ DashScope streaming task finished: ${this.currentTaskId}`);
        this.taskStarted = false;
        this.taskState = TaskState.FINISHED;

        // 🔥 连接复用：保持WebSocket连接，不关闭
        console.log('🔄 Task finished, connection ready for reuse');
        break;

      case 'task-failed':
        const errorMessage = message.header?.error_message || 'Unknown error';
        console.error('❌ DashScope streaming task failed:', errorMessage);

        // 🔥 根据官方文档：task-failed后连接会被关闭，无法继续复用
        console.warn('🔌 Connection will be closed by server after task-failed, preparing for reconnection');

        // 重置任务状态
        this.taskStarted = false;
        this.taskState = TaskState.IDLE;
        this.pendingAudioBuffers = [];

        // 标记连接即将失效
        this.isConnected = false;

        if (this.streamingCallbacks) {
          this.streamingCallbacks.onError(
            this.createError(`DashScope task failed: ${errorMessage}`, 'TASK_FAILED', true)
          );
        }

        // 🔥 主动触发重连准备（因为服务端会关闭连接）
        setTimeout(() => {
          if (this.isStreaming) {
            console.log('🔄 Attempting to recover from task-failed by reconnecting...');
            this.handleConnectionLost();
          }
        }, 1000); // 1秒后尝试重连
        break;

      default:
        console.log('🔍 DashScope unknown streaming event:', event);
    }
  }

  /**
   * 🔧 发送run-task指令（流式模式）
   */
  private async sendRunTask(options?: ASROptions): Promise<void> {
    if (!this.ws || !this.isConnected) {
      throw new Error('DashScope WebSocket not connected');
    }

    // 生成32位随机任务ID
    this.currentTaskId = uuidv4().replace(/-/g, '').slice(0, 32);
    console.log(`🆔 Generated streaming task ID: ${this.currentTaskId}`);

    const runTaskMessage = {
      header: {
        action: 'run-task',
        task_id: this.currentTaskId,
        streaming: 'duplex'
      },
      payload: {
        task_group: 'audio',
        task: 'asr',
        function: 'recognition',
        model: this.dashscopeConfig.model,
        parameters: {
          sample_rate: options?.sampleRate || 16000,
          format: 'pcm',
          // 🔥 修复句子结束检测参数（严格按照官方文档）
          semantic_punctuation_enabled: false,  // 使用VAD断句，延迟更低，适合交互场景
          max_sentence_silence: 1500,  // 1.5秒静音判断句子结束（官方推荐范围200-6000ms）
          multi_threshold_mode_enabled: true,  // 防止VAD断句切割过长
          punctuation_prediction_enabled: true,  // 自动添加标点符号
          disfluency_removal_enabled: false,  // 不过滤语气词
          inverse_text_normalization_enabled: true,  // 开启ITN，数字转换
          heartbeat: true  // 启用心跳保持长连接
        },
        input: {}
      }
    };

    console.log('📤 Sending DashScope streaming run-task message:', JSON.stringify(runTaskMessage, null, 2));

    try {
      this.ws.send(JSON.stringify(runTaskMessage));
      console.log('✅ Streaming run-task message sent successfully');

      // 等待task-started事件，正确处理Promise rejection
      await this.waitForTaskStarted();
    } catch (error) {
      console.error('❌ Failed to send streaming run-task message:', {
        error,
        message: (error as Error)?.message || 'Unknown error',
        stack: (error as Error)?.stack,
        wsState: this.ws?.readyState,
        isConnected: this.isConnected
      });

      // 重置任务状态，避免卡在STARTING状态
      this.taskState = TaskState.IDLE;
      this.taskStarted = false;
      this.pendingAudioBuffers = [];

      // 🔥 修复：只有在真正的错误情况下才通知回调，避免误报
      // 如果是超时错误但实际上task-started事件可能即将到达，不要立即报错
      const errorMessage = (error as Error)?.message || 'Unknown error';
      if (!errorMessage.includes('waiting for task-started') || !this.isConnected) {
        // 只有在连接真正断开或其他严重错误时才通知回调
        if (this.streamingCallbacks) {
          this.streamingCallbacks.onError(
            this.createError(`Failed to start task: ${errorMessage}`, 'TASK_START_ERROR', true)
          );
        }
      } else {
        // 对于task-started等待超时，只记录日志，不通知回调（因为功能可能实际正常）
        console.warn('⚠️ Task-started wait timeout, but connection is still active. Continuing...');
      }

      // 🔥 不要抛出错误，让流程继续，因为DashScope可能实际上工作正常
      // throw new Error(`Failed to send run-task message: ${errorMessage}`);
    }
  }

  /**
   * 🔧 等待task-started事件（增强错误处理）
   */
  private async waitForTaskStarted(): Promise<void> {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      const maxAttempts = 100; // 10秒 / 100ms = 100次

      const timeout = setTimeout(() => {
        console.error(`❌ Timeout waiting for task-started event after ${maxAttempts * 100}ms`);
        console.error(`🔍 Current state: taskStarted=${this.taskStarted}, taskState=${this.taskState}, isConnected=${this.isConnected}`);

        // 重置状态
        this.taskState = TaskState.IDLE;
        this.taskStarted = false;

        reject(new Error(`Timeout waiting for task-started event. WebSocket state: ${this.ws?.readyState}, Connected: ${this.isConnected}`));
      }, 10000);  // 10秒超时

      const checkTaskStarted = () => {
        attempts++;

        // 🔥 修复：只在连接真正断开时才报错，不要在每次检查时都验证连接状态
        // WebSocket状态可能在短时间内波动，但不代表连接丢失
        if (this.taskStarted) {
          clearTimeout(timeout);
          console.log(`✅ Task started successfully after ${attempts * 100}ms`);
          resolve();
        } else if (attempts >= maxAttempts) {
          clearTimeout(timeout);
          // 🔥 最终检查：只有在真正无法连接时才报错
          if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket connection lost while waiting for task-started'));
          } else {
            reject(new Error('Max attempts reached waiting for task-started'));
          }
        } else {
          setTimeout(checkTaskStarted, 100);
        }
      };

      checkTaskStarted();
    });
  }

  /**
   * 🔧 发送finish-task指令
   */
  private async sendFinishTask(): Promise<void> {
    if (!this.ws || !this.currentTaskId) {
      console.log('⚠️ Cannot send finish-task: WebSocket not ready or no current task');
      return;
    }

    const finishTaskMessage = {
      header: {
        action: 'finish-task',
        task_id: this.currentTaskId,
        streaming: 'duplex'
      },
      payload: {
        input: {} // 🔥 修复：根据官方文档，finish-task只需要input字段
      }
    };

    console.log('📤 Sending finish-task message for streaming session');
    try {
      this.ws.send(JSON.stringify(finishTaskMessage));
      console.log('✅ Finish-task message sent successfully');
    } catch (error) {
      console.error('❌ Failed to send finish-task message:', error);
    }
  }
}
