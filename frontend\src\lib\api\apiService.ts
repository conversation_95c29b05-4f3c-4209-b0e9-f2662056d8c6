import useAuthStore from '../../stores/authStore';
import { jwtDecode } from 'jwt-decode';

// API 基础URL (API base URL)
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz/api' // 生产环境 API URL (使用完整域名)
  : '/api'; // 开发环境使用相对路径 (Use relative path for development)

// --- Type Definitions ---
export interface Resume {
  id: string;
  userId: number; // In backend, this is Int, matches User.id
  fileName: string;
  filePath: string;
  fileType?: string | null;
  fileSize?: number | null;
  jobTitle?: string | null;
  uploadTimestamp: string; // ISO DateTime string
  createdAt: string; // ISO DateTime string
  updatedAt: string; // ISO DateTime string
}

export interface ResumeCreateInput {
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
  jobTitle?: string;
}

export type ResumeUpdateInput = Partial<ResumeCreateInput>;

export interface TargetPosition {
  id: string;
  userId: string; // In backend, this is now String (UUID), matches User.id
  positionName: string;
  positionRequirements?: string | null;
  companyName?: string | null;
  companyProfile?: string | null;
  status?: string | null;
  createdAt: string; // ISO DateTime string
  updatedAt: string; // ISO DateTime string
}

export interface TargetPositionCreateInput {
  positionName: string;
  positionRequirements?: string;
  companyName: string; // 必填字段，与后端模型保持一致
  companyProfile?: string;
  status?: string; // 改为可选，后端会提供默认值
}

export type TargetPositionUpdateInput = Partial<TargetPositionCreateInput>;

// --- Helper function for authenticated API calls ---
interface FetchOptions extends RequestInit {
  body?: any; // Allow body to be any type, will be stringified if object
}

export async function fetchWithAuth<T>(endpoint: string, options: FetchOptions = {}): Promise<T> {
  const token = useAuthStore.getState().token;
  // 添加调试信息，确认 token 是否存在
  console.log(`fetchWithAuth: Token exists: ${!!token}`);
  if (token) {
    console.log(`fetchWithAuth: Token preview: ${token.substring(0, 20)}...`);
    try {
      // 尝试解码 token，确认其格式正确
      const decoded = jwtDecode(token);
      console.log('fetchWithAuth: Token decoded successfully:', decoded);
    } catch (error) {
      console.error('fetchWithAuth: Failed to decode token:', error);
    }
  } else {
    console.warn('fetchWithAuth: No token available, request will be unauthorized');
  }
  
  // 使用Record<string, string>类型来允许字符串索引
  const headers: Record<string, string> = {
    ...options.headers as Record<string, string>,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
    console.log('fetchWithAuth: Added Authorization header');
  }

  if (options.body && typeof options.body === 'object' && !(options.body instanceof FormData)) {
    headers['Content-Type'] = 'application/json';
    options.body = JSON.stringify(options.body);
  }

  try {
    console.log(`请求API: ${API_BASE_URL}${endpoint}`, { method: options.method, body: options.body });

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
    });

    console.log(`API响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      let errorData;
      let errorText = '';

      try {
        // 尝试解析为JSON
        const clonedResponse = response.clone();
        errorText = await clonedResponse.text();
        console.log('错误响应文本:', errorText);

        try {
          errorData = await response.json();
          console.log('错误响应数据:', errorData);
        } catch (jsonError) {
          console.log('解析JSON错误:', jsonError);
          // 如果不是JSON，使用响应文本
          errorData = { message: errorText || response.statusText };
        }
      } catch (e) {
        console.log('获取错误文本失败:', e);
        errorData = { message: response.statusText };
      }

      const errorMessage = errorData?.message || `API请求失败，状态码: ${response.status}`;
      console.error(`API错误: ${errorMessage}`, errorData);
      throw new Error(errorMessage);
    }

    // 处理204 No Content响应（通常用于DELETE操作）
    if (response.status === 204) {
      return null as T;
    }

    // 解析响应数据
    const data = await response.json();
    console.log(`API响应数据:`, data);
    return data as T;

  } catch (error) {
    // 捕获并重新抛出所有错误，包括网络错误和解析错误
    console.error(`API调用错误 (${endpoint}):`, error);
    throw error;
  }
}

// --- Resume API Service ---
export const resumeService = {
  createResume: (data: ResumeCreateInput): Promise<Resume> => {
    return fetchWithAuth<Resume>('/resumes', { method: 'POST', body: data });
  },
  getResumes: async (): Promise<Resume[]> => {
    const response = await fetchWithAuth<{ success: boolean; data: { resumes: Resume[] } }>('/resumes', { method: 'GET' });
    return response.data.resumes;
  },
  updateResume: (id: string, data: ResumeUpdateInput): Promise<Resume> => {
    return fetchWithAuth<Resume>(`/resumes/${id}`, { method: 'PUT', body: data });
  },
  deleteResume: (id: string): Promise<void> => {
    return fetchWithAuth<void>(`/resumes/${id}`, { method: 'DELETE' });
  },
};

// --- Target Position API Service ---
export const targetPositionService = {
  createTargetPosition: async (data: TargetPositionCreateInput): Promise<TargetPosition> => {
    // 🎯 字段映射：前端使用 positionName/companyName，后端期望 title/company
    const mappedData = {
      title: data.positionName,
      company: data.companyName,
      description: data.companyProfile,
      requirements: data.positionRequirements,
      status: data.status
    };

    const response = await fetchWithAuth<{ success: boolean; message: string; data: any }>('/positions', { method: 'POST', body: mappedData });

    // 🔍 调试：查看创建岗位的响应
    console.log('🔍 createTargetPosition response:', response);
    console.log('🔍 createTargetPosition response.data:', response.data);

    // 🎯 提取并映射返回的数据：后端格式 → 前端格式
    // 🔧 修复：后端返回 data.position，不是直接 data
    const backendData = response.data.position;
    const mappedPosition = {
      id: backendData.id,
      userId: backendData.userId || '',
      positionName: backendData.title,
      companyName: backendData.company,
      companyProfile: backendData.description,
      positionRequirements: backendData.requirements,
      status: backendData.status,
      createdAt: backendData.createdAt,
      updatedAt: backendData.updatedAt
    };

    console.log('🔍 createTargetPosition mapped result:', mappedPosition);
    return mappedPosition;
  },
  getTargetPositions: async (): Promise<TargetPosition[]> => {
    const response = await fetchWithAuth<{ success: boolean; data: { positions: any[] } }>('/positions', { method: 'GET' });

    // 🔍 调试：查看获取岗位列表的原始响应
    console.log('🔍 getTargetPositions response:', response);
    console.log('🔍 getTargetPositions response.data:', response.data);
    console.log('🔍 getTargetPositions response.data.positions:', response.data.positions);

    // 🎯 字段映射：后端返回 title/company，前端期望 positionName/companyName
    const mappedPositions = response.data.positions.map((pos, index) => {
      console.log(`🔍 getTargetPositions mapping position ${index}:`, pos);

      const mapped = {
        id: pos.id,
        userId: pos.userId || '',
        positionName: pos.title,
        companyName: pos.company,
        companyProfile: pos.description,
        positionRequirements: pos.requirements,
        status: pos.status,
        createdAt: pos.createdAt,
        updatedAt: pos.updatedAt
      };

      console.log(`🔍 getTargetPositions mapped result ${index}:`, mapped);
      return mapped;
    });

    return mappedPositions;
  },
  updateTargetPosition: async (id: string, data: TargetPositionUpdateInput): Promise<TargetPosition> => {
    // 🎯 字段映射：前端使用 positionName/companyName，后端期望 title/company
    const mappedData: any = {};
    if (data.positionName !== undefined) mappedData.title = data.positionName;
    if (data.companyName !== undefined) mappedData.company = data.companyName;
    if (data.companyProfile !== undefined) mappedData.description = data.companyProfile;
    if (data.positionRequirements !== undefined) mappedData.requirements = data.positionRequirements;
    if (data.status !== undefined) mappedData.status = data.status;

    const response = await fetchWithAuth<{ success: boolean; message: string; data: any }>(`/positions/${id}`, { method: 'PUT', body: mappedData });

    // 🎯 提取并映射返回的数据：后端格式 → 前端格式
    // 🔧 修复：后端返回 data.position，不是直接 data
    const backendData = response.data.position;
    return {
      id: backendData.id,
      userId: backendData.userId || '',
      positionName: backendData.title,
      companyName: backendData.company,
      companyProfile: backendData.description,
      positionRequirements: backendData.requirements,
      status: backendData.status,
      createdAt: backendData.createdAt,
      updatedAt: backendData.updatedAt
    };
  },
  deleteTargetPosition: (id: string): Promise<void> => {
    // 🛡️ 防御性编程：验证ID参数
    if (!id || id === 'undefined' || id === 'null') {
      throw new Error('删除失败：岗位ID无效');
    }

    console.log('🗑️ 删除岗位API调用，ID:', id);
    return fetchWithAuth<void>(`/positions/${id}`, { method: 'DELETE' });
  },

  // 🧹 清理无效岗位数据的工具函数
  cleanupInvalidPositions: async (): Promise<{ cleaned: number; remaining: number }> => {
    console.log('🧹 开始清理无效岗位数据...');

    const positions = await targetPositionService.getTargetPositions();
    const invalidPositions = positions.filter(pos => !pos.id || !pos.positionName);

    console.log(`🔍 发现 ${invalidPositions.length} 个无效岗位:`, invalidPositions);

    let cleanedCount = 0;
    for (const invalidPos of invalidPositions) {
      if (invalidPos.id) {
        try {
          await targetPositionService.deleteTargetPosition(invalidPos.id);
          cleanedCount++;
          console.log(`✅ 已删除无效岗位 ID: ${invalidPos.id}`);
        } catch (error) {
          console.error(`❌ 删除无效岗位失败 ID: ${invalidPos.id}`, error);
        }
      }
    }

    const remainingPositions = await targetPositionService.getTargetPositions();
    const remainingCount = remainingPositions.length;

    console.log(`🧹 清理完成: 删除了 ${cleanedCount} 个无效岗位，剩余 ${remainingCount} 个岗位`);

    return { cleaned: cleanedCount, remaining: remainingCount };
  },
};

// --- Interview API Service ---
export interface InterviewRecord {
  id: string;
  position: string;
  company: string;
  date: string;
  duration: string;
  score: number;
  status: 'completed' | 'in-progress' | 'cancelled';
}

export interface TranscriptEntry {
  speaker: string;
  content: string;
  timestamp: string;
}

export interface InterviewReviewData {
  sessionId: string;
  startedAt?: string;
  endedAt?: string;
  status?: string;
  position?: string;  // 添加岗位信息
  company?: string;   // 添加公司信息
  transcripts: TranscriptEntry[];
  aiSuggestions: any[]; // 暂时使用any[]，等待AISuggestion实现
}

// 🔥 新增：开始面试的请求和响应接口
export interface StartInterviewRequest {
  sessionId: string;
  positionId?: string;
}

export interface StartInterviewResponse {
  success: boolean;
  message: string;
  data: {
    sessionId: string;
    status: string;
    position: {
      companyName: string | null;
      positionName: string | null;
      titleJobInfo: string | null;
    };
    newBalance: {
      formalInterviewCredits: number;
      mockInterviewCredits: number;
      mianshijunBalance: number;
    };
  };
}

export interface EndInterviewResponse {
  success: boolean;
  message: string;
  data: {
    sessionId: string;
    status: string;
  };
}

export interface InterviewStatusResponse {
  success: boolean;
  data: {
    sessionId: string;
    status: string;
    createdAt: string;
    startedAt: string | null;
    endedAt: string | null;
    position: {
      companyName: string | null;
      positionName: string | null;
      titleJobInfo: string | null;
    };
  };
}

export const interviewService = {
  // 🔥 新增：开始正式面试API
  startFormalInterview: (request: StartInterviewRequest): Promise<StartInterviewResponse> => {
    return fetchWithAuth<StartInterviewResponse>('/interviews/start', {
      method: 'POST',
      body: request,
    });
  },

  // 🔥 新增：结束面试API
  endInterview: (sessionId: string): Promise<EndInterviewResponse> => {
    return fetchWithAuth<EndInterviewResponse>(`/interviews/${sessionId}/end`, {
      method: 'POST',
    });
  },

  // 🔥 新增：获取面试状态API
  getInterviewStatus: (sessionId: string): Promise<InterviewStatusResponse> => {
    return fetchWithAuth<InterviewStatusResponse>(`/interviews/${sessionId}/status`, {
      method: 'GET',
    });
  },

  // 原有API
  getInterviewRecords: (): Promise<InterviewRecord[]> => {
    return fetchWithAuth<InterviewRecord[]>('/interview-reviews', { method: 'GET' });
  },
  getInterviewReview: (sessionId: string): Promise<InterviewReviewData> => {
    return fetchWithAuth<InterviewReviewData>(`/interview-reviews/${sessionId}/review`, { method: 'GET' });
  },
};
